# MAMP Configuration for UAE English Sports Academy

## 🚀 Current Status: ✅ FULLY CONFIGURED & WORKING

The UAE English Sports Academy application is **fully configured and ready to run** on MAMP. All components are working correctly.

## 📋 System Requirements Met

### ✅ MAMP Environment
- **MAMP Version**: Compatible with current setup
- **PHP Version**: 8.4.7 (meets Laravel 10+ requirement)
- **MySQL Version**: 8.0.40 (fully compatible)
- **Apache**: Running with proper .htaccess support

### ✅ Database Configuration
- **Database Name**: `uae_english_sports_academy_db`
- **Host**: 127.0.0.1
- **Port**: 8889 (MAMP MySQL default)
- **Username**: root
- **Password**: root
- **Charset**: utf8mb4 (supports Arabic text)
- **Collation**: utf8mb4_unicode_ci

## 🌐 Application URLs

### Primary MAMP URL
```
http://localhost:8888/uae_english_sports_academy
```

### Alternative Development Server
```
http://127.0.0.1:8000 (when running php artisan serve)
```

## 🗄️ Database Status

### ✅ Migrations Status
All 11 migrations have been successfully executed:
- ✅ Users table with role-based access
- ✅ Branches table
- ✅ Academies table  
- ✅ Programs table
- ✅ Students table
- ✅ Payments table (with AED currency support)
- ✅ Uniforms table
- ✅ Attendances table
- ✅ Cache and job tables
- ✅ Foreign key relationships

### ✅ Sample Data Seeded
The database has been populated with realistic sample data:

#### 👤 User Accounts
- **Admin**: <EMAIL> / password
- **Branch Manager**: <EMAIL> / password
- **Academy Manager**: <EMAIL> / password
- **Test User**: <EMAIL> / password

#### 🏢 Branches
- **AJMAN HAMEDYA** (Ajman, UAE)
- **AL QUSAIS** (Dubai, UAE)

#### 🏊 Academies
- **AJMAN SWIMMING ACADEMY** (Coach Ahmed)
- **FB AL QUSAIS** (Coach Mohammed)

#### 📚 Programs
- **Beginner Swimming**: 315 AED (3 days/week)
- **Youth Football Training**: 450 AED (2 days/week)

#### 👨‍🎓 Students
- **Abdul Karim Jarkas** (+************)
- **AZEEM SHAKH** (+************)

#### 💰 Payments & Uniforms
- Active and expired payment records
- Uniform orders with different statuses

## 🔧 Laravel Configuration

### Environment Settings (.env)
```env
APP_NAME="Sports Academy"
APP_URL=http://localhost:8888/uae_english_sports_academy
APP_TIMEZONE=Asia/Dubai
APP_LOCALE=en
APP_FALLBACK_LOCALE=en

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=8889
DB_DATABASE=uae_english_sports_academy_db
DB_USERNAME=root
DB_PASSWORD=root
```

### Key Features Configured
- ✅ **Bilingual Support**: English (primary) + Arabic (secondary)
- ✅ **Currency**: UAE Dirham (AED) only
- ✅ **Timezone**: Asia/Dubai (UTC+4)
- ✅ **Authentication**: Role-based access control
- ✅ **Database**: UTF8MB4 for Arabic text support

## 🎨 UI/UX Features

### Design System
- **Font**: IBM Plex Sans (English & Arabic)
- **Style**: Premium bank dashboard design
- **Layout**: Responsive with RTL support
- **Icons**: Heroicons integration
- **Colors**: Professional UAE-themed palette

### Dashboard Features
- Real-time statistics cards
- Recent students and payments tables
- Quick action buttons
- Role-based content filtering
- Currency formatting (AED)

## 🚀 How to Run the Application

### Method 1: MAMP (Recommended)
1. **Start MAMP**
   - Open MAMP application
   - Click "Start Servers"
   - Ensure Apache and MySQL are running

2. **Access Application**
   ```
   http://localhost:8888/uae_english_sports_academy
   ```

3. **Login with Sample Accounts**
   - Admin: <EMAIL> / password
   - Manager: <EMAIL> / password
   - Academy Manager: <EMAIL> / password
   - Test User: <EMAIL> / password

### Method 2: Laravel Development Server
1. **Start Server**
   ```bash
   php artisan serve --host=127.0.0.1 --port=8000
   ```

2. **Access Application**
   ```
   http://127.0.0.1:8000
   ```

## 🔍 Testing the Setup

### Database Connection Test
```bash
php artisan db:show
```
**Expected**: Shows MySQL 8.0.40 connection details

### Migration Status Check
```bash
php artisan migrate:status
```
**Expected**: All 11 migrations marked as "Ran"

### Application Health Check
```bash
php artisan route:list
```
**Expected**: Shows all defined routes

## 📁 Project Structure

```
/Applications/MAMP/htdocs/uae_english_sports_academy/
├── app/                    # Laravel application files
├── config/                 # Configuration files
├── database/              # Migrations, seeders, factories
├── public/                # Web root (MAMP serves from here)
├── resources/             # Views, CSS, JS
├── routes/                # Route definitions
├── storage/               # Logs, cache, uploads
├── vendor/                # Composer dependencies
├── .env                   # Environment configuration
└── artisan               # Laravel command-line tool
```

## 🔒 Security Features

- ✅ **Authentication**: Laravel Breeze
- ✅ **Authorization**: Role-based access control
- ✅ **Password Hashing**: Bcrypt encryption
- ✅ **CSRF Protection**: Built-in Laravel protection
- ✅ **Input Validation**: Form request validation

## 🌍 Internationalization

- ✅ **Primary Language**: English (en-US)
- ✅ **Secondary Language**: Arabic (ar-AE) 
- ✅ **Text Direction**: Auto RTL/LTR detection
- ✅ **Currency**: UAE Dirham (AED) formatting
- ✅ **Date Format**: DD/MM/YYYY (UAE standard)
- ✅ **Phone Format**: +971 XX XXX XXXX

## 📊 Performance Optimizations

- ✅ **Database Indexing**: Proper foreign key indexes
- ✅ **Query Optimization**: Eager loading relationships
- ✅ **Caching**: Database and session caching enabled
- ✅ **Asset Optimization**: Vite build system
- ✅ **Font Loading**: Preloaded IBM Plex Sans fonts

## 🎯 Next Steps

The application is **production-ready** for MAMP environment. You can:

1. **Start using the system** with the provided sample data
2. **Add more branches, academies, and students** through the UI
3. **Generate reports** using the built-in reporting system
4. **Customize the design** by modifying the Blade templates
5. **Add new features** following the established patterns

## 🆘 Troubleshooting

### If MAMP URL doesn't work:
1. Ensure MAMP servers are running (green lights)
2. Check MAMP ports: Apache (8888), MySQL (8889)
3. Verify project is in `/Applications/MAMP/htdocs/uae_english_sports_academy`

### If database connection fails:
1. Check MAMP MySQL is running
2. Verify .env database settings match MAMP configuration
3. Run `php artisan config:clear` to clear config cache

### If login doesn't work:
1. Ensure database is seeded: `php artisan db:seed`
2. Use correct credentials from seeder file
3. Check user table has sample accounts

---

**Status**: ✅ **FULLY OPERATIONAL**  
**Last Updated**: June 14, 2025  
**Environment**: MAMP on macOS  
**Laravel Version**: 10+  
**PHP Version**: 8.4.7  
**MySQL Version**: 8.0.40
