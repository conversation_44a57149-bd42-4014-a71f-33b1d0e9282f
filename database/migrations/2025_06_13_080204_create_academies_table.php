<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('academies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->string('name')->charset('utf8mb4');
            $table->text('description')->charset('utf8mb4')->nullable();
            $table->string('coach_name')->charset('utf8mb4')->nullable();
            $table->string('coach_phone', 15)->nullable();
            $table->boolean('status')->default(true); // active/inactive
            $table->timestamps();

            // Indexes
            $table->index(['branch_id', 'status']);
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('academies');
    }
};
