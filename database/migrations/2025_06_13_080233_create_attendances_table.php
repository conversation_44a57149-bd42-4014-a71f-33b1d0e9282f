<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->date('date');
            $table->time('time');
            $table->enum('status', ['present', 'absent', 'late'])->default('present');
            $table->text('note')->charset('utf8mb4')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['student_id', 'date']);
            $table->index('date');
            $table->unique(['student_id', 'date']); // One attendance record per student per day
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendances');
    }
};
