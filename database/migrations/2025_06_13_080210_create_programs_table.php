<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('academy_id')->constrained()->onDelete('cascade');
            $table->string('name')->charset('utf8mb4');
            $table->json('days'); // JSON: ["FRI", "SAT", "SUN", "MON", "TUE", "WED", "THU"]
            $table->integer('classes')->default(1);
            $table->decimal('price', 10, 2); // AED with 2 decimal places
            $table->string('currency', 3)->default('AED');
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->integer('max_students')->nullable();
            $table->boolean('status')->default(true); // active/inactive
            $table->timestamps();

            // Indexes
            $table->index(['academy_id', 'status']);
            $table->index('name');
            $table->index('price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};
