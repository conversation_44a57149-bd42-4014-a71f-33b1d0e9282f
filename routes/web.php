<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\AcademyController;
use App\Http\Controllers\ProgramController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\UniformController;
use App\Http\Controllers\ReportController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

// Redirect root to dashboard if authenticated, otherwise to login
Route::get('/', function () {
    return Auth::check() ? redirect()->route('dashboard') : redirect()->route('login');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Branch Management (Admin and Branch Manager only)
    Route::middleware('role:admin,branch_manager')->group(function () {
        Route::resource('branches', BranchController::class);
        Route::post('branches/{branch}/toggle-status', [BranchController::class, 'toggleStatus'])->name('branches.toggle-status');
        Route::post('branches/bulk-action', [BranchController::class, 'bulkAction'])->name('branches.bulk-action');
        Route::get('branches/export/excel', [BranchController::class, 'exportExcel'])->name('branches.export.excel');
        Route::get('branches/export/pdf', [BranchController::class, 'exportPdf'])->name('branches.export.pdf');
    });

    // Branch API endpoints (for AJAX requests)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('branches', [BranchController::class, 'apiIndex'])->name('branches.index');
        Route::get('branches/statistics', [BranchController::class, 'getStatistics'])->name('branches.statistics');
    });

    // Academy Management
    Route::resource('academies', AcademyController::class);

    // Program Management
    Route::resource('programs', ProgramController::class);

    // Student Management
    Route::resource('students', StudentController::class);

    // Payment Management
    Route::resource('payments', PaymentController::class);

    // Uniform Management
    Route::resource('uniforms', UniformController::class);

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/financial', [ReportController::class, 'financial'])->name('financial');
        Route::get('/uniform', [ReportController::class, 'uniform'])->name('uniform');
        Route::get('/program', [ReportController::class, 'program'])->name('program');
        Route::get('/status', [ReportController::class, 'status'])->name('status');
        Route::get('/daily', [ReportController::class, 'daily'])->name('daily');
    });
});

require __DIR__ . '/auth.php';
