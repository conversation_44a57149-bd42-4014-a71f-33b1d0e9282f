@extends('layouts.dashboard')

@section('title', 'Dashboard - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Welcome Back!</h1>
                <p class="text-lg text-dark-gray">{{ Auth::user()->name }}</p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ ucfirst(Auth::user()->role ?? 'User') }}
                </span>
            </div>
        </div>
        <div class="text-right">
            <div class="bg-off-white rounded-lg p-4 inline-block">
                <div class="text-charcoal-black font-semibold mb-1">
                    <svg class="w-4 h-4 inline mr-2 text-dark-gray" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                        </path>
                    </svg>
                    Today
                </div>
                <div class="text-dark-gray">{{ now()->format('l, F j, Y') }}</div>
                <div class="text-dark-gray text-sm">{{ now()->format('g:i A') }}</div>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Bank-Style Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            @if (Auth::user()->role === 'admin' || Auth::user()->role === 'branch_manager')
                <!-- Total Branches -->
                <div class="stats-card scale-in" style="animation-delay: 0.1s;">
                    <div class="stats-icon">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value">{{ \App\Models\Branch::count() ?? 0 }}</div>
                    <div class="stats-label">Total Branches</div>
                    <div class="stats-change positive">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        Active & Growing
                    </div>
                </div>
            @endif

            <!-- Total Academies -->
            <div class="stats-card scale-in" style="animation-delay: 0.2s;">
                <div class="stats-icon bg-gradient-to-br from-success-green to-green-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">
                    @if (Auth::user()->role === 'admin')
                        {{ \App\Models\Academy::count() ?? 0 }}
                    @elseif(Auth::user()->role === 'branch_manager')
                        {{ \App\Models\Academy::where('branch_id', Auth::user()->branch_id)->count() ?? 0 }}
                    @else
                        {{ Auth::user()->academy_id ? 1 : 0 }}
                    @endif
                </div>
                <div class="stats-label">Total Academies</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                        </path>
                    </svg>
                    Excellence in Sports
                </div>
            </div>

            <!-- Total Students -->
            <div class="stats-card scale-in" style="animation-delay: 0.3s;">
                <div class="stats-icon bg-gradient-to-br from-gold-yellow to-yellow-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">
                    @if (Auth::user()->role === 'admin')
                        {{ \App\Models\Student::count() ?? 0 }}
                    @elseif(Auth::user()->role === 'branch_manager')
                        {{ \App\Models\Student::where('branch_id', Auth::user()->branch_id)->count() ?? 0 }}
                    @else
                        {{ \App\Models\Student::where('academy_id', Auth::user()->academy_id)->count() ?? 0 }}
                    @endif
                </div>
                <div class="stats-label">Total Students</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
                        </path>
                    </svg>
                    Future Champions
                </div>
            </div>

            <!-- Monthly Revenue -->
            <div class="stats-card scale-in" style="animation-delay: 0.4s;">
                <div class="stats-icon bg-gradient-to-br from-info-blue to-blue-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value text-leaders-red">
                    @php
                        $query = \App\Models\Payment::whereMonth('payment_date', now()->month)->whereYear(
                            'payment_date',
                            now()->year,
                        );

                        if (Auth::user()->role === 'branch_manager') {
                            $query->where('branch_id', Auth::user()->branch_id);
                        } elseif (Auth::user()->role === 'academy_manager') {
                            $query->where('academy_id', Auth::user()->academy_id);
                        }

                        $monthlyRevenue = $query->sum('amount') ?? 0;
                    @endphp
                    {{ number_format($monthlyRevenue, 0) }}
                </div>
                <div class="stats-label">Monthly Revenue (AED)</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    {{ now()->format('F Y') }}
                </div>
            </div>
        </div>

        <!-- Bank-Style Content Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Students -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Recent Students</h3>
                        <p class="bank-card-subtitle">Latest student registrations</p>
                    </div>
                    <a href="#" class="btn-bank">View All</a>
                </div>
                <div class="bank-card-body">
                    @php
                        $recentStudentsQuery = \App\Models\Student::with(['branch', 'academy'])
                            ->orderBy('created_at', 'desc')
                            ->limit(5);

                        if (Auth::user()->role === 'branch_manager') {
                            $recentStudentsQuery->where('branch_id', Auth::user()->branch_id);
                        } elseif (Auth::user()->role === 'academy_manager') {
                            $recentStudentsQuery->where('academy_id', Auth::user()->academy_id);
                        }

                        $recentStudents = $recentStudentsQuery->get();
                    @endphp

                    @if ($recentStudents->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="table-bank">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Academy</th>
                                        <th>Join Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($recentStudents as $student)
                                        <tr>
                                            <td class="font-semibold">{{ $student->full_name ?? 'N/A' }}</td>
                                            <td>{{ $student->academy->name ?? 'N/A' }}</td>
                                            <td>{{ $student->join_date ? $student->join_date->format('M d, Y') : 'N/A' }}
                                            </td>
                                            <td>
                                                <span
                                                    class="badge-bank {{ ($student->status ?? 'inactive') === 'active' ? 'badge-success' : 'badge-neutral' }}">
                                                    {{ ucfirst($student->status ?? 'Inactive') }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-medium-gray mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                </path>
                            </svg>
                            <p class="text-dark-gray">No students found.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Recent Payments</h3>
                        <p class="bank-card-subtitle">Latest payment transactions</p>
                    </div>
                    <a href="#" class="btn-bank">View All</a>
                </div>
                <div class="bank-card-body">
                    @php
                        $recentPaymentsQuery = \App\Models\Payment::with(['student', 'academy'])
                            ->orderBy('created_at', 'desc')
                            ->limit(5);

                        if (Auth::user()->role === 'branch_manager') {
                            $recentPaymentsQuery->where('branch_id', Auth::user()->branch_id);
                        } elseif (Auth::user()->role === 'academy_manager') {
                            $recentPaymentsQuery->where('academy_id', Auth::user()->academy_id);
                        }

                        $recentPayments = $recentPaymentsQuery->get();
                    @endphp

                    @if ($recentPayments->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="table-bank">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($recentPayments as $payment)
                                        <tr>
                                            <td class="font-semibold">{{ $payment->student->full_name ?? 'N/A' }}</td>
                                            <td class="font-bold text-leaders-red">
                                                {{ number_format($payment->amount ?? 0, 0) }} AED</td>
                                            <td>{{ $payment->payment_date ? $payment->payment_date->format('M d, Y') : 'N/A' }}
                                            </td>
                                            <td>
                                                @php
                                                    $status = $payment->status ?? 'pending';
                                                    $badgeClass = match ($status) {
                                                        'active' => 'badge-success',
                                                        'expired' => 'badge-error',
                                                        'pending' => 'badge-warning',
                                                        default => 'badge-neutral',
                                                    };
                                                @endphp
                                                <span class="badge-bank {{ $badgeClass }}">
                                                    {{ ucfirst($status) }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-medium-gray mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                                </path>
                            </svg>
                            <p class="text-dark-gray">No payments found.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Bank-Style Quick Actions -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">Quick Actions</h3>
                    <p class="bank-card-subtitle">Frequently used operations</p>
                </div>
            </div>
            <div class="bank-card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <button class="btn-bank w-full" onclick="openModal('create', 'student')">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add New Student
                    </button>
                    <button class="btn-bank btn-bank-secondary w-full" onclick="openModal('create', 'payment')">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                            </path>
                        </svg>
                        Add Payment
                    </button>
                    <button class="btn-bank btn-bank-outline w-full" onclick="openModal('create', 'uniform')">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        Order Uniform
                    </button>
                    <a href="#" class="btn-bank btn-bank-outline w-full">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg>
                        View Reports
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Modal functionality placeholder
        function openModal(action, type, id = null) {
            console.log(`Opening ${action} modal for ${type}`, id);
            // This will be implemented when modal system is created
            alert(`${action.charAt(0).toUpperCase() + action.slice(1)} ${type} modal will be implemented soon!`);
        }

        // Currency formatting for AED
        function formatCurrency(amount) {
            if (isNaN(amount) || amount === '') return '';
            return `${parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            })} AED`;
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Add any dashboard-specific initialization here
            console.log('Bank-style dashboard loaded successfully');
        });
    </script>
@endpush
