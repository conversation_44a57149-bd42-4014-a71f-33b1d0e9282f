@extends('layouts.app')

@section('title', 'Login - UAE English Sports Academy')

@section('content')
    <div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center py-4">
        <div class="row w-100 justify-content-center">
            <div class="col-11 col-sm-8 col-md-6 col-lg-5 col-xl-4">
                <!-- UAE English Sports Academy Login Card (Per UI Guide) -->
                <div class="card">
                    <!-- Clean Header (Per UI Guide) -->
                    <div class="text-center py-4 px-4"
                        style="background-color: var(--leaders-red); border-radius: var(--radius-card) var(--radius-card) 0 0;">
                        <div class="mb-3">
                            <img src="{{ asset('images/logo.jpg') }}" alt="UAE English Sports Academy Logo"
                                class="rounded-circle"
                                style="width: 80px; height: 80px; object-fit: cover; border: 3px solid white;">
                        </div>
                        <h1 class="text-white fw-bold mb-2"
                            style="font-size: 1.875rem; font-family: var(--font-family-primary);">Welcome Back</h1>
                        <p class="text-white mb-0" style="opacity: 0.9;">UAE English Sports Academy</p>
                    </div>
                    <!-- Form Body (Per UI Guide) -->
                    <div class="p-4">
                        <!-- Session Status -->
                        @if (session('status'))
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>{{ session('status') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('login') }}">
                            @csrf

                            <!-- Email Address (Per UI Guide) -->
                            <div class="mb-3">
                                <label for="email" class="form-label fw-medium" style="color: var(--charcoal-black);">
                                    Email Address
                                </label>
                                <input id="email" type="email"
                                    class="form-control @error('email') is-invalid @enderror" name="email"
                                    value="{{ old('email') }}" required autofocus autocomplete="username"
                                    placeholder="Enter your email address">
                                @error('email')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Password (Per UI Guide) -->
                            <div class="mb-3">
                                <label for="password" class="form-label fw-medium" style="color: var(--charcoal-black);">
                                    Password
                                </label>
                                <input id="password" type="password"
                                    class="form-control @error('password') is-invalid @enderror" name="password" required
                                    autocomplete="current-password" placeholder="Enter your password">
                                @error('password')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Remember Me & Forgot Password -->
                            <div class="mb-3 d-flex align-items-center justify-content-between">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember">
                                    <label class="form-check-label" for="remember_me">
                                        Remember me
                                    </label>
                                </div>
                                @if (Route::has('password.request'))
                                    <a class="text-decoration-none" href="{{ route('password.request') }}"
                                        style="color: var(--leaders-red);">
                                        Forgot password?
                                    </a>
                                @endif
                            </div>

                            <!-- Login Button (Per UI Guide) -->
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                </button>
                            </div>
                        </form>
                    </div>
                    <!-- Demo Credentials (Per UI Guide) -->
                    <div class="border-top p-3 text-center" style="background-color: var(--off-white);">
                        <h6 class="fw-medium mb-3" style="color: var(--dark-gray);">
                            Demo Accounts
                        </h6>
                        <div class="row g-2">
                            <div class="col-12 col-md-4">
                                <div class="demo-card p-2 border rounded"
                                    style="background-color: var(--pure-white); cursor: pointer;">
                                    <div class="fw-medium mb-1" style="color: var(--leaders-red);">
                                        <i class="fas fa-crown me-1"></i>Administrator
                                    </div>
                                    <small class="text-muted d-block"><EMAIL></small>
                                    <small class="text-muted">password</small>
                                </div>
                            </div>
                            <div class="col-12 col-md-4">
                                <div class="demo-card p-2 border rounded"
                                    style="background-color: var(--pure-white); cursor: pointer;">
                                    <div class="fw-medium mb-1" style="color: var(--success-green);">
                                        <i class="fas fa-building me-1"></i>Branch Manager
                                    </div>
                                    <small class="text-muted d-block"><EMAIL></small>
                                    <small class="text-muted">password</small>
                                </div>
                            </div>
                            <div class="col-12 col-md-4">
                                <div class="demo-card p-2 border rounded"
                                    style="background-color: var(--pure-white); cursor: pointer;">
                                    <div class="fw-medium mb-1" style="color: var(--info-blue);">
                                        <i class="fas fa-graduation-cap me-1"></i>Academy Manager
                                    </div>
                                    <small class="text-muted d-block"><EMAIL></small>
                                    <small class="text-muted">password</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        /* Login Page Styles (Per UI Guide) */
        .card {
            max-width: 500px;
            margin: 0 auto;
        }

        /* Custom Checkbox (Per UI Guide) */
        .form-check-input:checked {
            background-color: var(--leaders-red);
            border-color: var(--leaders-red);
        }

        .form-check-input:focus {
            box-shadow: 0 0 0 0.2rem rgba(229, 62, 62, 0.25);
        }

        /* Demo Account Cards (Per UI Guide) */
        .demo-card {
            transition: all 0.2s ease-in-out;
        }

        .demo-card:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
            border-color: var(--leaders-red) !important;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .card {
                margin: 1rem;
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        // Demo Account Click to Fill (Per UI Guide)
        document.addEventListener('DOMContentLoaded', function() {
            const demoCards = document.querySelectorAll('.demo-card');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            demoCards.forEach(card => {
                card.addEventListener('click', function() {
                    const email = this.querySelector('small.d-block').textContent;
                    emailInput.value = email;
                    passwordInput.value = 'password';

                    // Visual feedback (Per UI Guide)
                    this.style.backgroundColor = 'var(--leaders-red)';
                    this.style.color = 'white';

                    setTimeout(() => {
                        this.style.backgroundColor = 'var(--pure-white)';
                        this.style.color = '';
                    }, 800);
                });
            });
        });
    </script>
@endpush
