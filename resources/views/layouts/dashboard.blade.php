<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Sports Academy Dashboard')</title>

    <!-- Favicon -->
    <link rel="icon" type="image/jpeg" href="{{ asset('images/logo.jpg') }}">
    <link rel="apple-touch-icon" href="{{ asset('images/logo.jpg') }}">

    <!-- Fonts -->
    <link rel="preload"
        href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap"
        as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload"
        href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap"
        as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Fallback for non-JS users -->
    <noscript>
        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap">
        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap">
    </noscript>

    <!-- Heroicons -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>
    <script src="https://unpkg.com/heroicons@2.0.18/24/solid/index.js" type="module"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Styles -->
    @stack('styles')
</head>

<body class="font-sans antialiased" x-data="dashboardLayout()" x-init="init()">
    <div class="dashboard-layout">
        <!-- Mobile Overlay -->
        <div class="mobile-overlay" :class="{ 'active': sidebarOpen }" @click="sidebarOpen = false"></div>

        <!-- Header -->
        <header class="header">
            <!-- Mobile Menu Button -->
            <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden btn-bank-outline p-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16">
                    </path>
                </svg>
            </button>

            <!-- Logo Only -->
            <a href="{{ route('dashboard') }}" class="header-brand">
                <img src="{{ asset('images/logo.jpg') }}" alt="Sports Academy Logo" class="header-logo">
            </a>

            <!-- Header Actions -->
            <div class="header-actions">
                <!-- Language Toggle -->
                <div class="language-toggle" @click="toggleLanguage()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
                        </path>
                    </svg>
                    <span x-text="currentLang"></span>
                </div>

                <!-- User Profile -->
                <div class="user-profile" x-data="{ open: false }" @click.away="open = false">
                    <div class="user-avatar" @click="open = !open">
                        {{ substr(Auth::user()->name, 0, 1) }}
                    </div>
                    <div class="user-info">
                        <div class="user-name">{{ Auth::user()->name }}</div>
                        <div class="user-role">{{ ucfirst(Auth::user()->role ?? 'User') }}</div>
                    </div>
                    <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>

                    <!-- Dropdown Menu -->
                    <div x-show="open" x-transition
                        class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                        <a href="{{ route('profile.edit') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Profile
                        </a>
                        <hr class="my-1">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit"
                                class="w-full text-left block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1">
                                    </path>
                                </svg>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="sidebar" :class="{ 'sidebar-collapsed': sidebarCollapsed, 'mobile-open': sidebarOpen }">
            <!-- Sidebar Toggle -->
            <button @click="sidebarCollapsed = !sidebarCollapsed" class="sidebar-toggle hidden lg:flex">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>

            <!-- Sidebar Header -->
            <div class="sidebar-header" x-show="!sidebarCollapsed || window.innerWidth < 1024">
                <h3 class="text-lg font-semibold text-gray-800">Dashboard</h3>
                <p class="text-sm text-gray-600">{{ ucfirst(Auth::user()->role ?? 'User') }} Panel</p>
            </div>

            <!-- Navigation -->
            <nav class="sidebar-nav">
                @include('layouts.partials.sidebar-nav')
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            @hasSection('header')
                <div class="bank-card mb-6 fade-in-up">
                    @yield('header')
                </div>
            @endif

            <!-- Page Content -->
            <div class="fade-in-up" style="animation-delay: 0.1s;">
                @yield('content')
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="text-center text-sm text-gray-600">
                <p>&copy; {{ date('Y') }} Sports Academy. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <!-- Additional Scripts -->
    @stack('scripts')

    <!-- Dashboard Layout Script -->
    <script>
        function dashboardLayout() {
            return {
                sidebarOpen: false,
                sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
                currentLang: localStorage.getItem('language') || 'EN',

                init() {
                    // Handle window resize
                    window.addEventListener('resize', () => {
                        if (window.innerWidth >= 1024) {
                            this.sidebarOpen = false;
                        }
                    });

                    // Apply saved language
                    this.applyLanguage();
                },

                toggleLanguage() {
                    this.currentLang = this.currentLang === 'EN' ? 'AR' : 'EN';
                    localStorage.setItem('language', this.currentLang);
                    this.applyLanguage();
                },

                applyLanguage() {
                    const html = document.documentElement;
                    if (this.currentLang === 'AR') {
                        html.setAttribute('dir', 'rtl');
                        html.setAttribute('lang', 'ar');
                        document.body.classList.add('arabic-text');
                    } else {
                        html.setAttribute('dir', 'ltr');
                        html.setAttribute('lang', 'en');
                        document.body.classList.remove('arabic-text');
                    }
                },

                $watch: {
                    sidebarCollapsed(value) {
                        localStorage.setItem('sidebarCollapsed', value);
                    }
                }
            }
        }
    </script>
</body>

</html>
