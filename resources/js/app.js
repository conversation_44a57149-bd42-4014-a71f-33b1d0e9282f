import './bootstrap';

import Alpine from 'alpinejs';

window.Alpine = Alpine;

// UAE English Sports Academy - Bank-Style Dashboard JavaScript

// Dashboard Layout Management
window.dashboardLayout = function() {
    return {
        sidebarOpen: false,
        sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
        currentLang: localStorage.getItem('language') || 'EN',

        init() {
            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024) {
                    this.sidebarOpen = false;
                }
            });

            // Apply saved language
            this.applyLanguage();

            // Watch for sidebar collapse changes
            this.$watch('sidebarCollapsed', (value) => {
                localStorage.setItem('sidebarCollapsed', value);
            });
        },

        toggleLanguage() {
            this.currentLang = this.currentLang === 'EN' ? 'AR' : 'EN';
            localStorage.setItem('language', this.currentLang);
            this.applyLanguage();
        },

        applyLanguage() {
            const html = document.documentElement;
            if (this.currentLang === 'AR') {
                html.setAttribute('dir', 'rtl');
                html.setAttribute('lang', 'ar');
                document.body.classList.add('arabic-text');
            } else {
                html.setAttribute('dir', 'ltr');
                html.setAttribute('lang', 'en');
                document.body.classList.remove('arabic-text');
            }
        }
    }
};

// Currency Formatting Utilities
window.formatCurrency = function(amount, currency = 'AED') {
    if (isNaN(amount) || amount === '') return '';
    return `${parseFloat(amount).toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    })} ${currency}`;
};

// Modal Management (Placeholder for future implementation)
window.openModal = function(action, type, id = null) {
    console.log(`Opening ${action} modal for ${type}`, id);
    // This will be implemented when modal system is created
    alert(`${action.charAt(0).toUpperCase() + action.slice(1)} ${type} modal will be implemented soon!`);
};

// Bank-style animations and interactions
document.addEventListener('DOMContentLoaded', function() {
    // Staggered animations for stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Enhanced hover effects for bank cards
    const bankCards = document.querySelectorAll('.bank-card');
    bankCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Table row hover effects
    const tableRows = document.querySelectorAll('.table-bank tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'var(--off-white)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    console.log('UAE English Sports Academy - Bank-style dashboard loaded successfully');
});

Alpine.start();
