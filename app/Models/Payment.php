<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    protected $fillable = [
        'student_id',
        'branch_id',
        'academy_id',
        'amount',
        'discount',
        'currency',
        'payment_method',
        'payment_date',
        'start_date',
        'end_date',
        'status',
        'reset_num',
        'class_time_from',
        'class_time_to',
        'renewal',
        'note',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'discount' => 'decimal:2',
        'payment_date' => 'date',
        'start_date' => 'date',
        'end_date' => 'date',
        'renewal' => 'boolean',
    ];

    /**
     * Get the student that owns the payment.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the branch that owns the payment.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the payment.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }
}
