<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Program extends Model
{
    protected $fillable = [
        'academy_id',
        'name',
        'days',
        'classes',
        'price',
        'currency',
        'start_time',
        'end_time',
        'max_students',
        'status',
    ];

    protected $casts = [
        'days' => 'array',
        'price' => 'decimal:2',
        'classes' => 'integer',
        'max_students' => 'integer',
        'status' => 'boolean',
    ];

    /**
     * Get the academy that owns the program.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the formatted days string.
     */
    public function getFormattedDaysAttribute(): string
    {
        if (is_array($this->days)) {
            return implode(', ', $this->days);
        }
        return '';
    }
}
