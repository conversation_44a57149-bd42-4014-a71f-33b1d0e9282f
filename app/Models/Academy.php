<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Academy extends Model
{
    protected $fillable = [
        'branch_id',
        'name',
        'description',
        'coach_name',
        'coach_phone',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the branch that owns the academy.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the programs for the academy.
     */
    public function programs(): HasMany
    {
        return $this->hasMany(Program::class);
    }

    /**
     * Get the students for the academy.
     */
    public function students(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Student::class);
    }

    /**
     * Get the payments for the academy.
     */
    public function payments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the uniforms for the academy.
     */
    public function uniforms(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Uniform::class);
    }

    /**
     * Get the users for the academy.
     */
    public function users(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }
}
