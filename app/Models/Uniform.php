<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Uniform extends Model
{
    protected $fillable = [
        'student_id',
        'branch_id',
        'academy_id',
        'order_date',
        'size',
        'amount',
        'currency',
        'branch_status',
        'office_status',
        'payment_method',
        'note',
    ];

    protected $casts = [
        'order_date' => 'date',
        'amount' => 'decimal:2',
    ];

    /**
     * Get the student that owns the uniform.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the branch that owns the uniform.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the uniform.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }
}
