<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Student extends Model
{
    protected $fillable = [
        'branch_id',
        'academy_id',
        'full_name',
        'email',
        'phone',
        'nationality',
        'address',
        'birth_date',
        'join_date',
        'status',
        'notes',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'join_date' => 'date',
    ];

    /**
     * Get the branch that owns the student.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the student.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the payments for the student.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the uniforms for the student.
     */
    public function uniforms(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Uniform::class);
    }

    /**
     * Get the attendance records for the student.
     */
    public function attendances(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Attendance::class);
    }
}
